package handlers

import (
	"payment-service/internal/pkg/config"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	authUseCase    interface{} // TODO: Replace with actual auth use case interface
	paymentUseCase interface{} // TODO: Replace with actual payment use case interface
	config         *config.Config
}

// NewHandlers creates a new handlers instance
func NewHandlers(authUseCase, paymentUseCase interface{}, config *config.Config) *Handlers {
	return &Handlers{
		authUseCase:    authUseCase,
		paymentUseCase: paymentUseCase,
		config:         config,
	}
}
