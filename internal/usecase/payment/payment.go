package payment

import (
	"payment-service/internal/pkg/config"
	"payment-service/pkg/contracts"
)

// PaymentUseCase handles payment business logic
type PaymentUseCase struct {
	transactionRepo interface{} // TODO: Replace with actual transaction repository interface
	eventBus        contracts.EventBus
	config          *config.Config
}

// NewPaymentUseCase creates a new payment use case
func NewPaymentUseCase(transactionRepo interface{}, eventBus contracts.EventBus, config *config.Config) *PaymentUseCase {
	return &PaymentUseCase{
		transactionRepo: transactionRepo,
		eventBus:        eventBus,
		config:          config,
	}
}
