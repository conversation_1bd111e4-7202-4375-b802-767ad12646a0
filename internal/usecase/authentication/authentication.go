package authentication

import (
	"payment-service/internal/pkg/config"
	"payment-service/pkg/contracts"
)

// AuthenticationUseCase handles authentication business logic
type AuthenticationUseCase struct {
	userRepo interface{} // TODO: Replace with actual user repository interface
	cache    contracts.Cache
	config   *config.Config
}

// NewAuthenticationUseCase creates a new authentication use case
func NewAuthenticationUseCase(userRepo interface{}, cache contracts.Cache, config *config.Config) *AuthenticationUseCase {
	return &AuthenticationUseCase{
		userRepo: userRepo,
		cache:    cache,
		config:   config,
	}
}
