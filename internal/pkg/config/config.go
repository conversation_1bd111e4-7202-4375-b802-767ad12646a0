package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/viper"
	"payment-service/pkg/contracts"
)

// Config represents the application configuration
type Config struct {
	App               AppConfig                            `mapstructure:"app"`
	Server            ServerConfig                         `mapstructure:"server"`
	Database          contracts.DatabaseConfig            `mapstructure:"database"`
	Broker            BrokerConfig                         `mapstructure:"broker"`
	Cache             contracts.CacheConfig               `mapstructure:"cache"`
	Auth              AuthConfig                           `mapstructure:"auth"`
	PaymentProviders  PaymentProvidersConfig              `mapstructure:"payment_providers"`
	Notifications     NotificationsConfig                  `mapstructure:"notifications"`
	Storage           StorageConfig                        `mapstructure:"storage"`
	Logging           LoggingConfig                        `mapstructure:"logging"`
	Monitoring        MonitoringConfig                     `mapstructure:"monitoring"`
	Security          SecurityConfig                       `mapstructure:"security"`
	Features          map[string]bool                      `mapstructure:"features"`
	Workers           WorkersConfig                        `mapstructure:"workers"`
}

// AppConfig represents application-level configuration
type AppConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
	Debug       bool   `mapstructure:"debug"`
	Port        int    `mapstructure:"port"`
	Host        string `mapstructure:"host"`
	Timezone    string `mapstructure:"timezone"`
}

// ServerConfig represents HTTP server configuration
type ServerConfig struct {
	ReadTimeout             time.Duration `mapstructure:"read_timeout"`
	WriteTimeout            time.Duration `mapstructure:"write_timeout"`
	IdleTimeout             time.Duration `mapstructure:"idle_timeout"`
	MaxHeaderBytes          int           `mapstructure:"max_header_bytes"`
	GracefulShutdownTimeout time.Duration `mapstructure:"graceful_shutdown_timeout"`
}

// BrokerConfig represents message broker configuration
type BrokerConfig struct {
	Primary  contracts.BrokerConfig `mapstructure:"primary"`
	Fallback contracts.BrokerConfig `mapstructure:"fallback"`
}

// AuthConfig represents authentication configuration
type AuthConfig struct {
	JWT          JWTConfig          `mapstructure:"jwt"`
	Session      SessionConfig      `mapstructure:"session"`
	RateLimiting RateLimitingConfig `mapstructure:"rate_limiting"`
}

// JWTConfig represents JWT configuration
type JWTConfig struct {
	Secret           string        `mapstructure:"secret"`
	Issuer           string        `mapstructure:"issuer"`
	Audience         string        `mapstructure:"audience"`
	AccessTokenTTL   time.Duration `mapstructure:"access_token_ttl"`
	RefreshTokenTTL  time.Duration `mapstructure:"refresh_token_ttl"`
	Algorithm        string        `mapstructure:"algorithm"`
}

// SessionConfig represents session configuration
type SessionConfig struct {
	Name     string `mapstructure:"name"`
	Secret   string `mapstructure:"secret"`
	MaxAge   int    `mapstructure:"max_age"`
	Secure   bool   `mapstructure:"secure"`
	HTTPOnly bool   `mapstructure:"http_only"`
	SameSite string `mapstructure:"same_site"`
}

// RateLimitingConfig represents rate limiting configuration
type RateLimitingConfig struct {
	Enabled            bool          `mapstructure:"enabled"`
	RequestsPerMinute  int           `mapstructure:"requests_per_minute"`
	BurstSize          int           `mapstructure:"burst_size"`
	CleanupInterval    time.Duration `mapstructure:"cleanup_interval"`
}

// PaymentProvidersConfig represents payment providers configuration
type PaymentProvidersConfig struct {
	Xendit   XenditConfig   `mapstructure:"xendit"`
	SNAP     SNAPConfig     `mapstructure:"snap"`
	Midtrans MidtransConfig `mapstructure:"midtrans"`
}

// XenditConfig represents Xendit configuration
type XenditConfig struct {
	Enabled      bool          `mapstructure:"enabled"`
	BaseURL      string        `mapstructure:"base_url"`
	APIKey       string        `mapstructure:"api_key"`
	WebhookToken string        `mapstructure:"webhook_token"`
	Timeout      time.Duration `mapstructure:"timeout"`
}

// SNAPConfig represents SNAP configuration
type SNAPConfig struct {
	Enabled        bool          `mapstructure:"enabled"`
	BaseURL        string        `mapstructure:"base_url"`
	ClientID       string        `mapstructure:"client_id"`
	ClientSecret   string        `mapstructure:"client_secret"`
	PrivateKeyPath string        `mapstructure:"private_key_path"`
	Timeout        time.Duration `mapstructure:"timeout"`
}

// MidtransConfig represents Midtrans configuration
type MidtransConfig struct {
	Enabled   bool          `mapstructure:"enabled"`
	BaseURL   string        `mapstructure:"base_url"`
	ServerKey string        `mapstructure:"server_key"`
	ClientKey string        `mapstructure:"client_key"`
	Timeout   time.Duration `mapstructure:"timeout"`
}

// NotificationsConfig represents notifications configuration
type NotificationsConfig struct {
	Email   EmailConfig   `mapstructure:"email"`
	SMS     SMSConfig     `mapstructure:"sms"`
	Webhook WebhookConfig `mapstructure:"webhook"`
}

// EmailConfig represents email configuration
type EmailConfig struct {
	Provider string     `mapstructure:"provider"`
	SMTP     SMTPConfig `mapstructure:"smtp"`
}

// SMTPConfig represents SMTP configuration
type SMTPConfig struct {
	Host        string `mapstructure:"host"`
	Port        int    `mapstructure:"port"`
	Username    string `mapstructure:"username"`
	Password    string `mapstructure:"password"`
	FromAddress string `mapstructure:"from_address"`
	FromName    string `mapstructure:"from_name"`
}

// SMSConfig represents SMS configuration
type SMSConfig struct {
	Provider string       `mapstructure:"provider"`
	Twilio   TwilioConfig `mapstructure:"twilio"`
}

// TwilioConfig represents Twilio configuration
type TwilioConfig struct {
	AccountSID string `mapstructure:"account_sid"`
	AuthToken  string `mapstructure:"auth_token"`
	FromNumber string `mapstructure:"from_number"`
}

// WebhookConfig represents webhook configuration
type WebhookConfig struct {
	Timeout    time.Duration `mapstructure:"timeout"`
	MaxRetries int           `mapstructure:"max_retries"`
	RetryDelay time.Duration `mapstructure:"retry_delay"`
}

// StorageConfig represents file storage configuration
type StorageConfig struct {
	Type  string      `mapstructure:"type"`
	Local LocalConfig `mapstructure:"local"`
	S3    S3Config    `mapstructure:"s3"`
}

// LocalConfig represents local storage configuration
type LocalConfig struct {
	BasePath    string `mapstructure:"base_path"`
	MaxFileSize int64  `mapstructure:"max_file_size"`
}

// S3Config represents S3 storage configuration
type S3Config struct {
	Region    string `mapstructure:"region"`
	Bucket    string `mapstructure:"bucket"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// MonitoringConfig represents monitoring configuration
type MonitoringConfig struct {
	Metrics     MetricsConfig     `mapstructure:"metrics"`
	HealthCheck HealthCheckConfig `mapstructure:"health_check"`
	Tracing     TracingConfig     `mapstructure:"tracing"`
}

// MetricsConfig represents metrics configuration
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Path    string `mapstructure:"path"`
}

// HealthCheckConfig represents health check configuration
type HealthCheckConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Path    string `mapstructure:"path"`
}

// TracingConfig represents tracing configuration
type TracingConfig struct {
	Enabled        bool   `mapstructure:"enabled"`
	JaegerEndpoint string `mapstructure:"jaeger_endpoint"`
}

// SecurityConfig represents security configuration
type SecurityConfig struct {
	CORS       CORSConfig       `mapstructure:"cors"`
	Encryption EncryptionConfig `mapstructure:"encryption"`
	APIKeys    APIKeysConfig    `mapstructure:"api_keys"`
}

// CORSConfig represents CORS configuration
type CORSConfig struct {
	Enabled          bool     `mapstructure:"enabled"`
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	ExposedHeaders   []string `mapstructure:"exposed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// EncryptionConfig represents encryption configuration
type EncryptionConfig struct {
	Key string `mapstructure:"key"`
}

// APIKeysConfig represents API keys configuration
type APIKeysConfig struct {
	HeaderName string `mapstructure:"header_name"`
	QueryParam string `mapstructure:"query_param"`
}

// WorkersConfig represents workers configuration
type WorkersConfig struct {
	PaymentProcessor  WorkerConfig `mapstructure:"payment_processor"`
	NotificationSender WorkerConfig `mapstructure:"notification_sender"`
	WebhookSender     WorkerConfig `mapstructure:"webhook_sender"`
}

// WorkerConfig represents individual worker configuration
type WorkerConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	Concurrency int    `mapstructure:"concurrency"`
	QueueName   string `mapstructure:"queue_name"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	v := viper.New()

	// Set default configuration file name and paths
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath("./configs")
	v.AddConfigPath("../configs")
	v.AddConfigPath("../../configs")

	// Set environment variable prefix
	v.SetEnvPrefix("PAYMENT_SERVICE")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// Read configuration file
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Load environment-specific configuration
	env := v.GetString("app.environment")
	if env != "" {
		envConfigFile := fmt.Sprintf("config.%s", env)
		v.SetConfigName(envConfigFile)
		
		if err := v.MergeInConfig(); err != nil {
			if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
				return nil, fmt.Errorf("failed to read environment config file: %w", err)
			}
		}
	}

	// Unmarshal configuration
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Validate configuration
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// validateConfig validates the configuration
func validateConfig(config *Config) error {
	if config.App.Name == "" {
		return fmt.Errorf("app.name is required")
	}

	if config.Database.Host == "" {
		return fmt.Errorf("database.host is required")
	}

	if config.Auth.JWT.Secret == "" {
		return fmt.Errorf("auth.jwt.secret is required")
	}

	if len(config.Security.Encryption.Key) != 32 {
		return fmt.Errorf("security.encryption.key must be exactly 32 characters")
	}

	return nil
}

// GetConfigPath returns the path to the configuration file
func GetConfigPath(env string) string {
	if env == "" {
		return "configs/config.yaml"
	}
	return filepath.Join("configs", fmt.Sprintf("config.%s.yaml", env))
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return strings.ToLower(c.App.Environment) == "production"
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return strings.ToLower(c.App.Environment) == "development"
}

// GetAddress returns the server address
func (c *Config) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.App.Host, c.App.Port)
}
