package rabbitmq

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/streadway/amqp"
	"payment-service/pkg/contracts"
)

// Publisher implements the contracts.Publisher interface for RabbitMQ
type Publisher struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	config  *Config
	metrics contracts.MetricsCollector
}

// NewPublisher creates a new RabbitMQ publisher
func NewPublisher(config *Config, metrics contracts.MetricsCollector) (*Publisher, error) {
	conn, err := amqp.Dial(config.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("failed to open channel: %w", err)
	}

	// Set QoS if configured
	if config.PrefetchCount > 0 {
		err = channel.Qos(config.PrefetchCount, 0, false)
		if err != nil {
			channel.Close()
			conn.Close()
			return nil, fmt.Errorf("failed to set QoS: %w", err)
		}
	}

	publisher := &Publisher{
		conn:    conn,
		channel: channel,
		config:  config,
		metrics: metrics,
	}

	return publisher, nil
}

// Publish publishes a single message
func (p *Publisher) Publish(ctx context.Context, message *contracts.Message, opts *contracts.PublishOptions) error {
	start := time.Now()
	defer func() {
		if p.metrics != nil {
			p.metrics.RecordLatency(opts.Topic, time.Since(start))
		}
	}()

	// Ensure exchange exists
	err := p.declareExchange(opts.Topic)
	if err != nil {
		if p.metrics != nil {
			p.metrics.IncrementErrors(opts.Topic, "exchange_declaration")
		}
		return fmt.Errorf("failed to declare exchange: %w", err)
	}

	// Prepare AMQP message
	amqpMsg := amqp.Publishing{
		MessageId:    message.ID,
		Timestamp:    message.Timestamp,
		ContentType:  "application/json",
		Body:         message.Body,
		Headers:      make(amqp.Table),
		DeliveryMode: amqp.Transient, // Default to non-persistent
	}

	// Set persistence if requested
	if opts.Persistent {
		amqpMsg.DeliveryMode = amqp.Persistent
	}

	// Set priority if specified
	if opts.Priority > 0 {
		amqpMsg.Priority = uint8(opts.Priority)
	}

	// Set TTL if specified
	if opts.TTL > 0 {
		amqpMsg.Expiration = fmt.Sprintf("%d", opts.TTL.Milliseconds())
	}

	// Add headers
	for key, value := range message.Headers {
		amqpMsg.Headers[key] = value
	}
	for key, value := range opts.Headers {
		amqpMsg.Headers[key] = value
	}

	// Add retry information
	amqpMsg.Headers["retry_count"] = message.Retry
	amqpMsg.Headers["max_retries"] = message.MaxRetries

	// Handle delayed messages
	if opts.Delay > 0 {
		return p.publishDelayed(ctx, &amqpMsg, opts)
	}

	// Publish message
	routingKey := opts.Key
	if routingKey == "" {
		routingKey = message.Key
	}

	err = p.channel.Publish(
		opts.Topic, // exchange
		routingKey, // routing key
		false,      // mandatory
		false,      // immediate
		amqpMsg,
	)

	if err != nil {
		if p.metrics != nil {
			p.metrics.IncrementErrors(opts.Topic, "publish")
		}
		return fmt.Errorf("failed to publish message: %w", err)
	}

	if p.metrics != nil {
		p.metrics.IncrementPublished(opts.Topic)
	}

	return nil
}

// PublishBatch publishes multiple messages in a batch
func (p *Publisher) PublishBatch(ctx context.Context, messages []*contracts.Message, opts *contracts.PublishOptions) error {
	if len(messages) == 0 {
		return nil
	}

	start := time.Now()
	defer func() {
		if p.metrics != nil {
			p.metrics.RecordLatency(opts.Topic, time.Since(start))
		}
	}()

	// Ensure exchange exists
	err := p.declareExchange(opts.Topic)
	if err != nil {
		if p.metrics != nil {
			p.metrics.IncrementErrors(opts.Topic, "exchange_declaration")
		}
		return fmt.Errorf("failed to declare exchange: %w", err)
	}

	// Publish all messages
	for _, message := range messages {
		err := p.Publish(ctx, message, opts)
		if err != nil {
			return fmt.Errorf("failed to publish message %s: %w", message.ID, err)
		}
	}

	return nil
}

// publishDelayed publishes a delayed message using RabbitMQ delayed message plugin
func (p *Publisher) publishDelayed(ctx context.Context, msg *amqp.Publishing, opts *contracts.PublishOptions) error {
	// Add delay header for RabbitMQ delayed message plugin
	if msg.Headers == nil {
		msg.Headers = make(amqp.Table)
	}
	msg.Headers["x-delay"] = int64(opts.Delay.Milliseconds())

	routingKey := opts.Key
	if routingKey == "" {
		routingKey = ""
	}

	err := p.channel.Publish(
		opts.Topic+".delayed", // exchange with delayed suffix
		routingKey,            // routing key
		false,                 // mandatory
		false,                 // immediate
		*msg,
	)

	if err != nil {
		if p.metrics != nil {
			p.metrics.IncrementErrors(opts.Topic, "publish_delayed")
		}
		return fmt.Errorf("failed to publish delayed message: %w", err)
	}

	return nil
}

// declareExchange declares an exchange if it doesn't exist
func (p *Publisher) declareExchange(exchangeName string) error {
	return p.channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
}

// Close closes the publisher connection
func (p *Publisher) Close() error {
	if p.channel != nil {
		p.channel.Close()
	}
	if p.conn != nil {
		return p.conn.Close()
	}
	return nil
}

// HealthCheck checks if the publisher is healthy
func (p *Publisher) HealthCheck(ctx context.Context) error {
	if p.conn == nil || p.conn.IsClosed() {
		return fmt.Errorf("RabbitMQ connection is closed")
	}
	if p.channel == nil {
		return fmt.Errorf("RabbitMQ channel is nil")
	}
	return nil
}
