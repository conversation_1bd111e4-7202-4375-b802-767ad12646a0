package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"

	"payment-service/internal/infrastructure/broker"
	"payment-service/internal/infrastructure/cache/redis"
	"payment-service/internal/infrastructure/database/postgres"
	"payment-service/internal/interfaces/http/handlers"
	"payment-service/internal/interfaces/http/middleware"
	"payment-service/internal/interfaces/http/routes"
	"payment-service/internal/pkg/config"
	"payment-service/internal/usecase/authentication"
	"payment-service/internal/usecase/payment"
	"payment-service/pkg/contracts"
)

// Application represents the main application
type Application struct {
	config   *config.Config
	fiber    *fiber.App
	db       contracts.DatabaseConnection
	cache    contracts.Cache
	broker   contracts.EventBus
	handlers *handlers.Handlers
}

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create application
	app, err := NewApplication(cfg)
	if err != nil {
		log.Fatalf("Failed to create application: %v", err)
	}

	// Start application
	if err := app.Start(); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}
}

// NewApplication creates a new application instance
func NewApplication(cfg *config.Config) (*Application, error) {
	// Initialize database connection
	db, err := postgres.NewConnection(&cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Run database migrations
	if err := db.Migrate(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to run database migrations: %w", err)
	}

	// Initialize cache
	cacheFactory := redis.NewFactory()
	cache, err := cacheFactory.CreateCache(&cfg.Cache)
	if err != nil {
		return nil, fmt.Errorf("failed to create cache: %w", err)
	}

	// Initialize message broker
	brokerFactory := broker.NewFactory(nil) // TODO: Add metrics collector
	eventBus, err := brokerFactory.CreateEventBus(&cfg.Broker.Primary)
	if err != nil {
		return nil, fmt.Errorf("failed to create event bus: %w", err)
	}

	// Initialize repositories
	userRepo := postgres.NewUserRepository(db)
	companyRepo := postgres.NewCompanyRepository(db)
	transactionRepo := postgres.NewTransactionRepository(db)

	// Initialize use cases
	authUseCase := authentication.NewAuthenticationUseCase(userRepo, cache, cfg)
	paymentUseCase := payment.NewPaymentUseCase(transactionRepo, eventBus, cfg)

	// Initialize handlers
	handlers := handlers.NewHandlers(authUseCase, paymentUseCase, cfg)

	// Initialize Fiber app
	fiberApp := fiber.New(fiber.Config{
		ReadTimeout:    cfg.Server.ReadTimeout,
		WriteTimeout:   cfg.Server.WriteTimeout,
		IdleTimeout:    cfg.Server.IdleTimeout,
		ReadBufferSize: cfg.Server.MaxHeaderBytes,
		ErrorHandler:   customErrorHandler,
	})

	// Setup middleware
	setupMiddleware(fiberApp, cfg)

	// Setup routes
	routes.SetupRoutes(fiberApp, handlers, cfg)

	return &Application{
		config:   cfg,
		fiber:    fiberApp,
		db:       db,
		cache:    cache,
		broker:   eventBus,
		handlers: handlers,
	}, nil
}

// Start starts the application
func (a *Application) Start() error {
	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start HTTP server in a goroutine
	go func() {
		log.Printf("Starting HTTP server on %s", a.config.GetAddress())
		if err := a.fiber.Listen(a.config.GetAddress()); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP server failed: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Create shutdown context with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, a.config.Server.GracefulShutdownTimeout)
	defer shutdownCancel()

	// Shutdown HTTP server
	if err := a.fiber.ShutdownWithContext(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}

	// Close database connection
	if err := a.db.Close(); err != nil {
		log.Printf("Database close error: %v", err)
	}

	// Close cache connection
	if err := a.cache.Close(); err != nil {
		log.Printf("Cache close error: %v", err)
	}

	// Close broker connection
	if err := a.broker.Close(); err != nil {
		log.Printf("Broker close error: %v", err)
	}

	log.Println("Server shutdown complete")
	return nil
}

// setupMiddleware sets up Fiber middleware
func setupMiddleware(app *fiber.App, cfg *config.Config) {
	// Request ID middleware
	app.Use(requestid.New())

	// Logger middleware
	if cfg.App.Debug {
		app.Use(logger.New(logger.Config{
			Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
		}))
	}

	// Recovery middleware
	app.Use(recover.New())

	// CORS middleware
	if cfg.Security.CORS.Enabled {
		app.Use(cors.New(cors.Config{
			AllowOrigins:     joinStrings(cfg.Security.CORS.AllowedOrigins, ","),
			AllowMethods:     joinStrings(cfg.Security.CORS.AllowedMethods, ","),
			AllowHeaders:     joinStrings(cfg.Security.CORS.AllowedHeaders, ","),
			ExposeHeaders:    joinStrings(cfg.Security.CORS.ExposedHeaders, ","),
			AllowCredentials: cfg.Security.CORS.AllowCredentials,
			MaxAge:           cfg.Security.CORS.MaxAge,
		}))
	}

	// Rate limiting middleware
	if cfg.Auth.RateLimiting.Enabled {
		app.Use(middleware.RateLimit(cfg))
	}

	// Security headers middleware
	app.Use(middleware.SecurityHeaders())

	// Request logging middleware
	app.Use(middleware.RequestLogging())
}

// customErrorHandler handles Fiber errors
func customErrorHandler(c *fiber.Ctx, err error) error {
	// Default error code
	code := fiber.StatusInternalServerError

	// Check if it's a Fiber error
	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
	}

	// Log error
	log.Printf("Error: %v", err)

	// Return error response
	return c.Status(code).JSON(fiber.Map{
		"error": true,
		"message": err.Error(),
		"code": code,
		"timestamp": time.Now().Unix(),
	})
}

// joinStrings joins a slice of strings with a separator
func joinStrings(slice []string, sep string) string {
	if len(slice) == 0 {
		return ""
	}
	
	result := slice[0]
	for i := 1; i < len(slice); i++ {
		result += sep + slice[i]
	}
	return result
}

// Health check endpoint
func healthCheck(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"status": "ok",
		"timestamp": time.Now().Unix(),
		"version": "2.0.0",
	})
}

// Metrics endpoint placeholder
func metrics(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"metrics": "TODO: Implement metrics collection",
	})
}
