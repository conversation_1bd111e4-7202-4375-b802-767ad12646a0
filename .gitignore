# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
/bin/
/dist/
/build/
/tmp/
/temp/

# Logs
*.log
/logs/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Configuration files with secrets
configs/config.production.yaml
configs/config.staging.yaml
configs/*.secret.yaml

# Database files
*.db
*.sqlite
*.sqlite3

# Storage directories
/storage/
/uploads/

# Docker
.dockerignore

# Coverage reports
coverage.txt
coverage.html
coverage.out

# Profiling data
*.prof
*.pprof

# Air live reload
tmp/

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup files
*.bak
*.backup

# Compiled Object files
*.o
*.a

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

# cgo generated files
_cgo_gotypes.go
_cgo_export.*

# Test coverage
profile.out

# Delve debugger
__debug_bin

# GoLand
.idea/

# Payment service specific
payment-service-*.txt
*.dump
