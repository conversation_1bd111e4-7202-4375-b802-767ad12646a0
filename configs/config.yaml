# Payment Service Configuration
# Environment: development

app:
  name: "payment-service"
  version: "2.0.0"
  environment: "development"
  debug: true
  port: 8080
  host: "0.0.0.0"
  timezone: "Asia/Jakarta"
  
server:
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s
  max_header_bytes: 1048576 # 1MB
  graceful_shutdown_timeout: 30s

database:
  driver: "postgres"
  host: "localhost"
  port: 5432
  database: "payment_service"
  username: "postgres"
  password: "password"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s
  conn_max_idle_time: 60s
  timezone: "Asia/Jakarta"

# Message Broker Configuration
broker:
  # Primary broker (can be: rabbitmq, kafka, redis, nats)
  primary:
    type: "rabbitmq"
    url: "amqp://guest:guest@localhost:5672/"
    username: "guest"
    password: "guest"
    max_retries: 3
    retry_delay: 5s
    connect_timeout: 30s
    request_timeout: 30s
    extra:
      prefetch_count: "10"
      
  # Fallback broker (optional)
  fallback:
    type: "redis"
    url: "redis://localhost:6379"
    password: ""
    max_retries: 3
    retry_delay: 2s
    connect_timeout: 10s
    request_timeout: 10s
    extra:
      database: "0"
      pool_size: "10"

# Cache Configuration
cache:
  type: "redis"
  address: "localhost:6379"
  password: ""
  database: 1
  pool_size: 10
  min_idle_conns: 2
  max_retries: 3
  retry_delay: 1s
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  pool_timeout: 4s
  idle_timeout: 300s
  max_conn_age: 3600s
  default_ttl: 3600s
  key_prefix: "payment_service:"
  serialization: "json"

# Authentication Configuration
auth:
  jwt:
    secret: "your-super-secret-jwt-key-change-in-production"
    issuer: "payment-service"
    audience: "payment-service-users"
    access_token_ttl: 15m
    refresh_token_ttl: 168h # 7 days
    algorithm: "HS256"
    
  session:
    name: "payment_session"
    secret: "your-session-secret-change-in-production"
    max_age: 86400 # 24 hours
    secure: false # Set to true in production with HTTPS
    http_only: true
    same_site: "lax"
    
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_size: 10
    cleanup_interval: 60s

# Payment Providers Configuration
payment_providers:
  xendit:
    enabled: true
    base_url: "https://api.xendit.co"
    api_key: "your-xendit-api-key"
    webhook_token: "your-xendit-webhook-token"
    timeout: 30s
    
  snap:
    enabled: true
    base_url: "https://api.snap.bi"
    client_id: "your-snap-client-id"
    client_secret: "your-snap-client-secret"
    private_key_path: "/path/to/snap/private-key.pem"
    timeout: 30s
    
  midtrans:
    enabled: false
    base_url: "https://api.sandbox.midtrans.com"
    server_key: "your-midtrans-server-key"
    client_key: "your-midtrans-client-key"
    timeout: 30s

# Notification Configuration
notifications:
  email:
    provider: "smtp" # smtp, mailgun, sendgrid
    smtp:
      host: "localhost"
      port: 587
      username: "<EMAIL>"
      password: "your-email-password"
      from_address: "<EMAIL>"
      from_name: "Payment Service"
      
  sms:
    provider: "twilio" # twilio, nexmo
    twilio:
      account_sid: "your********account-sid"
      auth_token: "your********auth-token"
      from_number: "+**********"
      
  webhook:
    timeout: 30s
    max_retries: 5
    retry_delay: 30s
    
# File Storage Configuration
storage:
  type: "local" # local, s3, gcs
  local:
    base_path: "./storage"
    max_file_size: ******** # 10MB
    
  s3:
    region: "us-east-1"
    bucket: "payment-service-files"
    access_key: "your-aws-access-key"
    secret_key: "your-aws-secret-key"
    
# Logging Configuration
logging:
  level: "debug" # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, file
  file_path: "./logs/app.log"
  max_size: 100 # MB
  max_backups: 5
  max_age: 30 # days
  compress: true

# Monitoring Configuration
monitoring:
  metrics:
    enabled: true
    path: "/metrics"
    
  health_check:
    enabled: true
    path: "/health"
    
  tracing:
    enabled: false
    jaeger_endpoint: "http://localhost:14268/api/traces"
    
# Security Configuration
security:
  cors:
    enabled: true
    allowed_origins: ["http://localhost:3000", "https://yourdomain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]
    exposed_headers: ["X-Total-Count"]
    allow_credentials: true
    max_age: 86400
    
  encryption:
    key: "your-32-character-encryption-key!!" # Must be 32 characters
    
  api_keys:
    header_name: "X-API-Key"
    query_param: "api_key"

# Feature Flags
features:
  new_payment_flow: false
  enhanced_logging: true
  rate_limiting: true
  webhook_retries: true
  audit_logging: true

# Worker Configuration
workers:
  payment_processor:
    enabled: true
    concurrency: 5
    queue_name: "payment_processing"
    
  notification_sender:
    enabled: true
    concurrency: 3
    queue_name: "notifications"
    
  webhook_sender:
    enabled: true
    concurrency: 2
    queue_name: "webhooks"
