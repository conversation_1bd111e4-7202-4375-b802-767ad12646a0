package contracts

import (
	"context"
	"time"
)

// Message represents a message in the broker system
type Message struct {
	ID         string            `json:"id"`
	Topic      string            `json:"topic"`
	Key        string            `json:"key,omitempty"`
	Body       []byte            `json:"body"`
	<PERSON>ers    map[string]string `json:"headers,omitempty"`
	Timestamp  time.Time         `json:"timestamp"`
	Retry      int               `json:"retry"`
	MaxRetries int               `json:"max_retries"`
}

// PublishOptions contains options for publishing messages
type PublishOptions struct {
	Topic      string
	Key        string
	Headers    map[string]string
	Delay      time.Duration
	TTL        time.Duration
	Persistent bool
	Priority   int
}

// SubscribeOptions contains options for subscribing to messages
type SubscribeOptions struct {
	Topic           string
	ConsumerGroup   string
	AutoAck         bool
	PrefetchCount   int
	MaxRetries      int
	RetryDelay      time.Duration
	DeadLetterTopic string
}

// Publisher interface for publishing messages
type Publisher interface {
	Publish(ctx context.Context, message *Message, opts *PublishOptions) error
	PublishBatch(ctx context.Context, messages []*Message, opts *PublishOptions) error
	Close() error
}

// Subscriber interface for subscribing to messages
type Subscriber interface {
	Subscribe(ctx context.Context, opts *SubscribeOptions, handler MessageHandler) error
	Unsubscribe(topic string) error
	Close() error
}

// MessageHandler function type for handling messages
type MessageHandler func(ctx context.Context, message *Message) error

// EventBus interface for domain event publishing/subscribing
type EventBus interface {
	Publisher
	Subscriber
	PublishEvent(ctx context.Context, event DomainEvent) error
	SubscribeToEvent(ctx context.Context, eventType string, handler EventHandler) error
}

// DomainEvent represents a domain event
type DomainEvent interface {
	EventType() string
	EventID() string
	EventTime() time.Time
	AggregateID() string
	EventData() interface{}
}

// EventHandler function type for handling domain events
type EventHandler func(ctx context.Context, event DomainEvent) error

// Queue interface for work queue functionality
type Queue interface {
	Enqueue(ctx context.Context, queueName string, message *Message) error
	Dequeue(ctx context.Context, queueName string, handler MessageHandler) error
	GetQueueSize(ctx context.Context, queueName string) (int, error)
	PurgeQueue(ctx context.Context, queueName string) error
}

// Stream interface for event streaming (Kafka-like)
type Stream interface {
	Produce(ctx context.Context, streamName string, message *Message) error
	Consume(ctx context.Context, streamName string, consumerGroup string, handler MessageHandler) error
	CreateStream(ctx context.Context, streamName string, partitions int) error
	DeleteStream(ctx context.Context, streamName string) error
}

// BrokerConfig represents broker configuration
type BrokerConfig struct {
	Type           string            `yaml:"type"` // rabbitmq, kafka, redis, nats
	URL            string            `yaml:"url"`
	Username       string            `yaml:"username"`
	Password       string            `yaml:"password"`
	MaxRetries     int               `yaml:"max_retries"`
	RetryDelay     time.Duration     `yaml:"retry_delay"`
	ConnectTimeout time.Duration     `yaml:"connect_timeout"`
	RequestTimeout time.Duration     `yaml:"request_timeout"`
	Extra          map[string]string `yaml:"extra"`
}

// MetricsCollector interface for broker metrics
type MetricsCollector interface {
	IncrementPublished(topic string)
	IncrementConsumed(topic string)
	IncrementErrors(topic string, errorType string)
	RecordLatency(topic string, duration time.Duration)
}

// BrokerFactory interface for creating broker instances
type BrokerFactory interface {
	CreatePublisher(config *BrokerConfig) (Publisher, error)
	CreateSubscriber(config *BrokerConfig) (Subscriber, error)
	CreateEventBus(config *BrokerConfig) (EventBus, error)
	CreateQueue(config *BrokerConfig) (Queue, error)
	CreateStream(config *BrokerConfig) (Stream, error)
}
