-- Migration: 001_initial_schema (DOWN)
-- Description: Rollback initial database schema for payment service
-- Author: System
-- Date: 2025-06-25

-- Drop triggers first
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_companies_updated_at ON companies;
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
DROP TRIGGER IF EXISTS update_permissions_updated_at ON permissions;
DROP TRIGGER IF EXISTS update_payment_providers_updated_at ON payment_providers;
DROP TRIGGER IF EXISTS update_payment_channels_updated_at ON payment_channels;
DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
DROP TRIGGER IF EXISTS update_webhooks_updated_at ON webhooks;
DROP TRIGGER IF EXISTS update_api_rate_limits_updated_at ON api_rate_limits;

-- Drop trigger function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_status;
DROP INDEX IF EXISTS idx_users_deleted_at;

DROP INDEX IF EXISTS idx_companies_api_key;
DROP INDEX IF EXISTS idx_companies_status;
DROP INDEX IF EXISTS idx_companies_deleted_at;

DROP INDEX IF EXISTS idx_transactions_uuid;
DROP INDEX IF EXISTS idx_transactions_company_id;
DROP INDEX IF EXISTS idx_transactions_external_id;
DROP INDEX IF EXISTS idx_transactions_status;
DROP INDEX IF EXISTS idx_transactions_type;
DROP INDEX IF EXISTS idx_transactions_created_at;

DROP INDEX IF EXISTS idx_transaction_logs_transaction_id;
DROP INDEX IF EXISTS idx_transaction_logs_created_at;

DROP INDEX IF EXISTS idx_webhooks_status;
DROP INDEX IF EXISTS idx_webhooks_next_attempt_at;
DROP INDEX IF EXISTS idx_webhooks_company_id;

DROP INDEX IF EXISTS idx_payment_channels_company_id;
DROP INDEX IF EXISTS idx_payment_channels_provider_id;
DROP INDEX IF EXISTS idx_payment_channels_is_active;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS api_rate_limits;
DROP TABLE IF EXISTS webhooks;
DROP TABLE IF EXISTS transaction_logs;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS payment_channels;
DROP TABLE IF EXISTS payment_providers;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS companies;
DROP TABLE IF EXISTS users;

-- Drop enum types
DROP TYPE IF EXISTS webhook_status;
DROP TYPE IF EXISTS payment_method;
DROP TYPE IF EXISTS transaction_type;
DROP TYPE IF EXISTS transaction_status;
DROP TYPE IF EXISTS company_status;
DROP TYPE IF EXISTS user_status;

-- Drop extensions (be careful with this in production)
-- DROP EXTENSION IF EXISTS "uuid-ossp";
