-- Migration: 002_add_audit_tables (DOWN)
-- Description: Rollback audit logging tables
-- Author: System
-- Date: 2025-06-25

-- Drop audit triggers
DROP TRIGGER IF EXISTS audit_users ON users;
DROP TRIGGER IF EXISTS audit_companies ON companies;
DROP TRIGGER IF EXISTS audit_transactions ON transactions;
DROP TRIGGER IF EXISTS audit_payment_channels ON payment_channels;

-- Drop audit function
DROP FUNCTION IF EXISTS create_audit_log();

-- Drop updated_at triggers
DROP TRIGGER IF EXISTS update_system_configurations_updated_at ON system_configurations;
DROP TRIGGER IF EXISTS update_feature_flags_updated_at ON feature_flags;
DROP TRIGGER IF EXISTS update_notification_templates_updated_at ON notification_templates;
DROP TRIGGER IF EXISTS update_notification_queue_updated_at ON notification_queue;
DROP TRIGGER IF EXISTS update_user_sessions_updated_at ON user_sessions;

-- Drop indexes
DROP INDEX IF EXISTS idx_audit_logs_user_id;
DROP INDEX IF EXISTS idx_audit_logs_company_id;
DROP INDEX IF EXISTS idx_audit_logs_action;
DROP INDEX IF EXISTS idx_audit_logs_resource_type;
DROP INDEX IF EXISTS idx_audit_logs_created_at;
DROP INDEX IF EXISTS idx_audit_logs_request_id;

DROP INDEX IF EXISTS idx_system_configurations_key;
DROP INDEX IF EXISTS idx_system_configurations_is_public;

DROP INDEX IF EXISTS idx_feature_flags_name;
DROP INDEX IF EXISTS idx_feature_flags_is_enabled;

DROP INDEX IF EXISTS idx_notification_templates_name;
DROP INDEX IF EXISTS idx_notification_templates_type;
DROP INDEX IF EXISTS idx_notification_templates_is_active;

DROP INDEX IF EXISTS idx_notification_queue_status;
DROP INDEX IF EXISTS idx_notification_queue_next_attempt_at;
DROP INDEX IF EXISTS idx_notification_queue_created_at;

DROP INDEX IF EXISTS idx_health_checks_service_name;
DROP INDEX IF EXISTS idx_health_checks_status;
DROP INDEX IF EXISTS idx_health_checks_checked_at;

DROP INDEX IF EXISTS idx_api_request_logs_company_id;
DROP INDEX IF EXISTS idx_api_request_logs_path;
DROP INDEX IF EXISTS idx_api_request_logs_response_status;
DROP INDEX IF EXISTS idx_api_request_logs_created_at;

DROP INDEX IF EXISTS idx_user_sessions_user_id;
DROP INDEX IF EXISTS idx_user_sessions_session_id;
DROP INDEX IF EXISTS idx_user_sessions_is_active;
DROP INDEX IF EXISTS idx_user_sessions_expires_at;

DROP INDEX IF EXISTS idx_password_reset_tokens_token;
DROP INDEX IF EXISTS idx_password_reset_tokens_user_id;
DROP INDEX IF EXISTS idx_password_reset_tokens_expires_at;

DROP INDEX IF EXISTS idx_email_verification_tokens_token;
DROP INDEX IF EXISTS idx_email_verification_tokens_user_id;
DROP INDEX IF EXISTS idx_email_verification_tokens_expires_at;

-- Drop tables
DROP TABLE IF EXISTS email_verification_tokens;
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS api_request_logs;
DROP TABLE IF EXISTS health_checks;
DROP TABLE IF EXISTS notification_queue;
DROP TABLE IF EXISTS notification_templates;
DROP TABLE IF EXISTS feature_flags;
DROP TABLE IF EXISTS system_configurations;
DROP TABLE IF EXISTS audit_logs;

-- Drop enum types
DROP TYPE IF EXISTS audit_action;
